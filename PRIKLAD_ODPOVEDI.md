# Příklady XML odpovědí ze serveru

Tento soubor obsahuje příklady XML odpovědí, kter<PERSON> mServer vrací po úspěšném vytvoření dokladů.

## Úspěšná odpověď - Zálohová faktura

```xml
<?xml version="1.0" encoding="UTF-8"?>
<dat:dataPack version="2.0" 
              id="ADV001" 
              ico="12345678" 
              application="PohodaMServerTester" 
              note="Zálohová faktura" 
              xmlns:dat="http://www.stormware.cz/schema/version_2/data.xsd">
    
    <dat:dataPackItem version="2.0" id="ADV001" state="ok">
        <dat:number>ZF202501011234</dat:number>
        <dat:id>150100025</dat:id>
    </dat:dataPackItem>
</dat:dataPack>
```

## Úspěšná odpověď - <PERSON>n<PERSON> do<PERSON>

```xml
<?xml version="1.0" encoding="UTF-8"?>
<dat:dataPack version="2.0" 
              id="INT001" 
              ico="12345678" 
              application="PohodaMServerTester" 
              note="Interní doklad" 
              xmlns:dat="http://www.stormware.cz/schema/version_2/data.xsd">
    
    <dat:dataPackItem version="2.0" id="INT001" state="ok">
        <dat:number>ID202501011234</dat:number>
        <dat:id>150100026</dat:id>
    </dat:dataPackItem>
</dat:dataPack>
```

## Úspěšná odpověď - Vydaná faktura

```xml
<?xml version="1.0" encoding="UTF-8"?>
<dat:dataPack version="2.0" 
              id="ISS001" 
              ico="12345678" 
              application="PohodaMServerTester" 
              note="Vydaná faktura s odpočtem zálohy" 
              xmlns:dat="http://www.stormware.cz/schema/version_2/data.xsd">
    
    <dat:dataPackItem version="2.0" id="ISS001" state="ok">
        <dat:number>FA202501011234</dat:number>
        <dat:id>150100027</dat:id>
    </dat:dataPackItem>
</dat:dataPack>
```

## Chybová odpověď

```xml
<?xml version="1.0" encoding="UTF-8"?>
<dat:dataPack version="2.0" 
              id="ADV001" 
              ico="12345678" 
              application="PohodaMServerTester" 
              note="Zálohová faktura" 
              xmlns:dat="http://www.stormware.cz/schema/version_2/data.xsd">
    
    <dat:dataPackItem version="2.0" id="ADV001" state="error">
        <dat:error>
            <dat:message>Chyba při vytváření dokladu</dat:message>
            <dat:code>1001</dat:code>
        </dat:error>
    </dat:dataPackItem>
</dat:dataPack>
```

## Parsování v aplikaci

Aplikace hledá v odpovědích:

1. **Atribut state** - musí být "ok" pro úspěšné vytvoření
2. **Element number** - číslo vytvořeného dokladu
3. **Element id** - interní ID dokladu v POHODĚ

Tyto informace se pak používají pro vytváření vazeb v následujících dokladech.
