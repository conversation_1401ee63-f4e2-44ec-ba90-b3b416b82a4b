using System;
using System.Globalization;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.Linq;
using System;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Sockets;
using System.Xml;
using System.Xml.Serialization;

namespace PohodaMServerTester
{
    public class MServerClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _serverUrl;
        private readonly string _ico;
        private readonly Configuration _config;
        private readonly string _credentials;

        // Ukládání ID vytvořených dokladů
        public string? AdvanceInvoiceId { get; private set; }
        public string? AdvanceInvoiceNumber { get; private set; }
        public string? InternalDocumentId { get; private set; }
        public string? InternalDocumentNumber { get; private set; }
        public string? IssuedInvoiceId { get; private set; }
        public string? IssuedInvoiceNumber { get; private set; }
        public string? LiquidationId { get; private set; }

        // Scénář B: Příjemka → Přijatá faktura
        public string? ReceiptId { get; private set; }
        public string? ReceiptNumber { get; private set; }
        public string? ReceivedInvoiceId { get; private set; }
        public string? ReceivedInvoiceNumber { get; private set; }

        private HttpRequestMessage GetRequest()
        {
            var request = new HttpRequestMessage(HttpMethod.Post, _serverUrl);
            request.Headers.Add("STW-Authorization", "Basic " + _credentials);
            request.Headers.Add("STW-Application", "Test");

            return request;
        }

        public MServerClient(string serverUrl, string username, string password, string ico, Configuration config)
        {
            _serverUrl = serverUrl.TrimEnd('/');
            _ico = ico;
            _config = config;

            _httpClient = new HttpClient();

            // Nastavení Basic Authentication
            _credentials = Convert.ToBase64String(Encoding.GetEncoding("ISO-8859-1").GetBytes($"{username}:{password}"));
        }

        public async Task<bool> TestConnection()
        {
            try
            {
                var request = GetRequest();
                request.Content = new StringContent(CreateTestXml(), Encoding.GetEncoding("Windows-1250"), "application/xml");
                var response = await _httpClient.SendAsync(request);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> SendXmlRequest(string xmlContent, string? documentType = null)
        {
            try
            {
                var request = GetRequest();
                request.Content = new StringContent(xmlContent, Encoding.GetEncoding("Windows-1250"), "application/xml");
                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();

                    // Parsování odpovědi pro získání ID dokladu
                    if (!string.IsNullOrEmpty(documentType))
                    {
                        ParseDocumentResponse(responseContent, documentType);
                    }

                    return responseContent;
                }
                else
                {
                    return $"Chyba HTTP: {response.StatusCode} - {response.ReasonPhrase}";
                }
            }
            catch (Exception ex)
            {
                return $"Chyba při odesílání: {ex.Message}";
            }
        }

        private void ParseDocumentResponse(string xmlResponse, string documentType)
        {
            try
            {
                var doc = XDocument.Parse(xmlResponse);
                var ns = XNamespace.Get("http://www.stormware.cz/schema/version_2/response.xsd");

                // Hledání informací o vytvořeném dokladu
                var dataPackItems = doc.Descendants(ns + "responsePackItem");

                foreach (var item in dataPackItems)
                {
                    var stateAttr = item.Attribute("state");
                    if (stateAttr?.Value == "ok")
                    {
                        // Hledání ID v producedDetails
                        var producedDetails = item.Descendants().FirstOrDefault(e => e.Name.LocalName == "producedDetails");
                        var idElement = producedDetails?.Descendants().FirstOrDefault(e => e.Name.LocalName == "id");
                        var numberElement = item.Descendants().FirstOrDefault(e => e.Name.LocalName == "number");

                        if (idElement != null)
                        {
                            switch (documentType.ToLower())
                            {
                                case "advance":
                                    AdvanceInvoiceId = idElement.Value;
                                    AdvanceInvoiceNumber = numberElement?.Value ?? $"ZF{DateTime.Now:yyyyMMddHHmm}";
                                    break;
                                case "internal":
                                    InternalDocumentId = idElement.Value;
                                    InternalDocumentNumber = numberElement?.Value ?? $"ID{DateTime.Now:yyyyMMddHHmm}";
                                    break;
                                case "issued":
                                    IssuedInvoiceId = idElement.Value;
                                    IssuedInvoiceNumber = numberElement?.Value ?? $"FA{DateTime.Now:yyyyMMddHHmm}";
                                    break;
                                case "receipt":
                                    ReceiptId = idElement.Value;
                                    ReceiptNumber = numberElement?.Value ?? $"PR{DateTime.Now:yyyyMMddHHmm}";
                                    break;
                                case "received":
                                    ReceivedInvoiceId = idElement.Value;
                                    ReceivedInvoiceNumber = numberElement?.Value ?? $"FP{DateTime.Now:yyyyMMddHHmm}";
                                    break;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Chyba při parsování odpovědi: {ex.Message}");
            }
        }

        private string CreateTestXml()
        {
            return $@"<?xml version=""1.0"" encoding=""Windows-1250""?>
<dat:dataPack version=""2.0""
              id=""001""
              ico=""{_ico}""
              application=""PohodaMServerTester""
              note=""Test connection""
              xmlns:dat=""http://www.stormware.cz/schema/version_2/data.xsd""
              xmlns:typ=""http://www.stormware.cz/schema/version_2/type.xsd"">
</dat:dataPack>";
        }

        public string CreateAdvanceInvoiceXml()
        {
            return $@"<?xml version=""1.0"" encoding=""Windows-1250""?>
<dat:dataPack version=""2.0""
              id=""ADV001""
              ico=""{_ico}""
              application=""PohodaMServerTester""
              note=""Zalohova faktura""
              xmlns:dat=""http://www.stormware.cz/schema/version_2/data.xsd""
              xmlns:inv=""http://www.stormware.cz/schema/version_2/invoice.xsd""
              xmlns:typ=""http://www.stormware.cz/schema/version_2/type.xsd"">

    <dat:dataPackItem version=""2.0"" id=""ADV001"">
        <inv:invoice version=""2.0"">
            <inv:invoiceHeader>
                <inv:invoiceType>issuedAdvanceInvoice</inv:invoiceType>
                <inv:number>
                    <typ:numberRequested>ZF{DateTime.Now:yyyyMMddHHmm}</typ:numberRequested>
                </inv:number>
                <inv:date>{DateTime.Now:yyyy-MM-dd}</inv:date>
                <inv:dateTax>{DateTime.Now:yyyy-MM-dd}</inv:dateTax>
                <inv:dateAccounting>{DateTime.Now:yyyy-MM-dd}</inv:dateAccounting>
                <inv:text>Zalohova faktura - testovaci</inv:text>
                <inv:partnerIdentity>
                    <typ:address>
                        <typ:company>{_config.TestData.Customer.Company}</typ:company>
                        <typ:city>{_config.TestData.Customer.City}</typ:city>
                        <typ:street>{_config.TestData.Customer.Street}</typ:street>
                        <typ:zip>{_config.TestData.Customer.Zip}</typ:zip>
                        <typ:ico>{_config.TestData.Customer.Ico}</typ:ico>
                        <typ:dic>{_config.TestData.Customer.Dic}</typ:dic>
                    </typ:address>
                </inv:partnerIdentity>
                <inv:myIdentity>
                    <typ:address>
                        <typ:company>{_config.TestData.MyCompany.Company}</typ:company>
                    </typ:address>
                </inv:myIdentity>
                <inv:paymentType>
                    <typ:paymentType>draft</typ:paymentType>
                </inv:paymentType>
                <inv:account>
                    <typ:accountNo>{_config.TestData.MyCompany.AccountNo}</typ:accountNo>
                    <typ:bankCode>{_config.TestData.MyCompany.BankCode}</typ:bankCode>
                </inv:account>
                <inv:symVar>VS{DateTime.Now:yyyyMMddHHmm}</inv:symVar>
                <inv:dateDue>{DateTime.Now.AddDays(30):yyyy-MM-dd}</inv:dateDue>
            </inv:invoiceHeader>

            <inv:invoiceDetail>
                <inv:invoiceItem>
                    <inv:text>Zaloha na sluzby</inv:text>
                    <inv:quantity>1</inv:quantity>
                    <inv:unit>ks</inv:unit>
                    <inv:coefficient>1</inv:coefficient>
                    <inv:payVAT>false</inv:payVAT>
                    <inv:rateVAT>none</inv:rateVAT>
                    <inv:discountPercentage>0</inv:discountPercentage>
                    <inv:homeCurrency>
                        <typ:unitPrice>{_config.TestData.Amounts.AdvanceAmount.ToString("F2", CultureInfo.InvariantCulture)}</typ:unitPrice>
                    </inv:homeCurrency>
                    <inv:note>Zaloha bez DPH</inv:note>
                </inv:invoiceItem>
            </inv:invoiceDetail>

            <inv:invoiceSummary>
                <inv:homeCurrency>
                    <typ:priceNone>{_config.TestData.Amounts.AdvanceAmount.ToString("F2", CultureInfo.InvariantCulture)}</typ:priceNone>
                    <typ:priceLow>0.00</typ:priceLow>
                    <typ:priceHigh>0.00</typ:priceHigh>
                    <typ:priceHighVAT>0.00</typ:priceHighVAT>
                </inv:homeCurrency>
            </inv:invoiceSummary>
        </inv:invoice>
    </dat:dataPackItem>
</dat:dataPack>";
        }

        public string CreateInternalDocumentXml()
        {
            string sourceLiquidationXml = "";
            string noteText = "Doklad k prijate zaloze";

            // Pokud máme ID likvidace, přidáme vazbu na ni
            if (!string.IsNullOrEmpty(LiquidationId))
            {
                sourceLiquidationXml = $@"
            <int:taxDocument>
                <int:sourceLiquidation>
                    <typ:sourceItemId>{LiquidationId}</typ:sourceItemId>
                </int:sourceLiquidation>
            </int:taxDocument>";
                noteText += $" - vazba na likvidaci {LiquidationId} zálohové faktury {AdvanceInvoiceNumber}";
            }

            return $@"<?xml version=""1.0"" encoding=""Windows-1250""?>
<dat:dataPack version=""2.0""
              id=""INT001""
              ico=""{_ico}""
              application=""PohodaMServerTester""
              note=""Interni doklad""
              xmlns:dat=""http://www.stormware.cz/schema/version_2/data.xsd""
              xmlns:int=""http://www.stormware.cz/schema/version_2/intDoc.xsd""
              xmlns:typ=""http://www.stormware.cz/schema/version_2/type.xsd"">

    <dat:dataPackItem version=""2.0"" id=""INT001"">
        <int:intDoc version=""2.0"">{sourceLiquidationXml}
            <int:intDocHeader>
                <int:number>
                    <typ:numberRequested>ID{DateTime.Now:yyyyMMddHHmm}</typ:numberRequested>
                </int:number>
                <int:date>{DateTime.Now:yyyy-MM-dd}</int:date>
                <int:dateTax>{DateTime.Now:yyyy-MM-dd}</int:dateTax>
                <int:dateAccounting>{DateTime.Now:yyyy-MM-dd}</int:dateAccounting>
                <int:text>{noteText}</int:text>
                <int:partnerIdentity>
                    <typ:address>
                        <typ:company>{_config.TestData.Customer.Company}</typ:company>
                        <typ:city>{_config.TestData.Customer.City}</typ:city>
                        <typ:street>{_config.TestData.Customer.Street}</typ:street>
                        <typ:zip>{_config.TestData.Customer.Zip}</typ:zip>
                        <typ:ico>{_config.TestData.Customer.Ico}</typ:ico>
                        <typ:dic>{_config.TestData.Customer.Dic}</typ:dic>
                    </typ:address>
                </int:partnerIdentity>
                <int:myIdentity>
                    <typ:address>
                        <typ:company>{_config.TestData.MyCompany.Company}</typ:company>
                    </typ:address>
                </int:myIdentity>
                <int:intNote>{noteText}</int:intNote>
            </int:intDocHeader>

            <int:intDocDetail>
                <int:intDocItem>
                    <int:text>Prijata zaloha - uplatneni DPH</int:text>
                    <int:quantity>1</int:quantity>
                    <int:unit>ks</int:unit>
                    <int:coefficient>1</int:coefficient>
                    <int:payVAT>true</int:payVAT>
                    <int:rateVAT>high</int:rateVAT>
                    <int:discountPercentage>0</int:discountPercentage>
                    <int:homeCurrency>
                        <typ:unitPrice>{(_config.TestData.Amounts.AdvanceAmount / (1 + _config.TestData.Amounts.VatRate)).ToString("F2", CultureInfo.InvariantCulture)}</typ:unitPrice>
                    </int:homeCurrency>
                    <int:note>Zaklad DPH {(_config.TestData.Amounts.VatRate * 100).ToString("F0", CultureInfo.InvariantCulture)}%</int:note>
                </int:intDocItem>
            </int:intDocDetail>
        </int:intDoc>
    </dat:dataPackItem>
</dat:dataPack>";
        }

        public string CreateIssuedInvoiceXml()
        {
            // Použijeme skutečné číslo zálohové faktury, pokud je k dispozici
            string advanceInvoiceNumber = !string.IsNullOrEmpty(AdvanceInvoiceNumber)
                ? AdvanceInvoiceNumber
                : $"ZF{DateTime.Now:yyyyMMddHHmm}";

            // Vazba na zálohovou fakturu se řeší pomocí inv:invoiceAdvancePaymentItem v invoiceDetail

            return $@"<?xml version=""1.0"" encoding=""Windows-1250""?>
<dat:dataPack version=""2.0""
              id=""ISS001""
              ico=""{_ico}""
              application=""PohodaMServerTester""
              note=""Vydana faktura s odpoctem zalohy""
              xmlns:dat=""http://www.stormware.cz/schema/version_2/data.xsd""
              xmlns:inv=""http://www.stormware.cz/schema/version_2/invoice.xsd""
              xmlns:typ=""http://www.stormware.cz/schema/version_2/type.xsd"">

    <dat:dataPackItem version=""2.0"" id=""ISS001"">
        <inv:invoice version=""2.0"">
            <inv:invoiceHeader>
                <inv:invoiceType>issuedInvoice</inv:invoiceType>
                <inv:number>
                    <typ:numberRequested>FA{DateTime.Now:yyyyMMddHHmm}</typ:numberRequested>
                </inv:number>
                <inv:date>{DateTime.Now:yyyy-MM-dd}</inv:date>
                <inv:dateTax>{DateTime.Now:yyyy-MM-dd}</inv:dateTax>
                <inv:dateAccounting>{DateTime.Now:yyyy-MM-dd}</inv:dateAccounting>
                <inv:text>Faktura za poskytute sluzby</inv:text>
                <inv:partnerIdentity>
                    <typ:address>
                        <typ:company>{_config.TestData.Customer.Company}</typ:company>
                        <typ:city>{_config.TestData.Customer.City}</typ:city>
                        <typ:street>{_config.TestData.Customer.Street}</typ:street>
                        <typ:zip>{_config.TestData.Customer.Zip}</typ:zip>
                        <typ:ico>{_config.TestData.Customer.Ico}</typ:ico>
                        <typ:dic>{_config.TestData.Customer.Dic}</typ:dic>
                    </typ:address>
                </inv:partnerIdentity>
                <inv:myIdentity>
                    <typ:address>
                        <typ:company>{_config.TestData.MyCompany.Company}</typ:company>
                    </typ:address>
                </inv:myIdentity>
                <inv:paymentType>
                    <typ:paymentType>draft</typ:paymentType>
                </inv:paymentType>
                <inv:account>
                    <typ:accountNo>{_config.TestData.MyCompany.AccountNo}</typ:accountNo>
                    <typ:bankCode>{_config.TestData.MyCompany.BankCode}</typ:bankCode>
                </inv:account>
                <inv:symVar>VS{DateTime.Now:yyyyMMddHHmm}</inv:symVar>
                <inv:dateDue>{DateTime.Now.AddDays(30):yyyy-MM-dd}</inv:dateDue>
            </inv:invoiceHeader>

            <inv:invoiceDetail>
                <inv:invoiceItem>
                    <inv:text>Poskytute sluzby</inv:text>
                    <inv:quantity>1</inv:quantity>
                    <inv:unit>ks</inv:unit>
                    <inv:coefficient>1</inv:coefficient>
                    <inv:payVAT>true</inv:payVAT>
                    <inv:rateVAT>high</inv:rateVAT>
                    <inv:discountPercentage>0</inv:discountPercentage>
                    <inv:homeCurrency>
                        <typ:unitPrice>{_config.TestData.Amounts.ServiceAmount.ToString("F2", CultureInfo.InvariantCulture)}</typ:unitPrice>
                    </inv:homeCurrency>
                    <inv:note>Sluzby s DPH {(_config.TestData.Amounts.VatRate * 100).ToString("F0", CultureInfo.InvariantCulture)}%</inv:note>
                </inv:invoiceItem>

                <inv:invoiceAdvancePaymentItem>
                    <inv:sourceDocument>
                        <typ:number>{advanceInvoiceNumber}</typ:number>
                    </inv:sourceDocument>
                    <inv:quantity>1</inv:quantity>
                    <inv:payVAT>false</inv:payVAT>
                    <inv:rateVAT>none</inv:rateVAT>
                    <inv:homeCurrency>
                        <typ:unitPrice>-{_config.TestData.Amounts.AdvanceAmount.ToString("F2", CultureInfo.InvariantCulture)}</typ:unitPrice>
                        <typ:price>-{_config.TestData.Amounts.AdvanceAmount.ToString("F2", CultureInfo.InvariantCulture)}</typ:price>
                        <typ:priceVAT>0</typ:priceVAT>
                        <typ:priceSum>-{_config.TestData.Amounts.AdvanceAmount.ToString("F2", CultureInfo.InvariantCulture)}</typ:priceSum>
                    </inv:homeCurrency>
                </inv:invoiceAdvancePaymentItem>
            </inv:invoiceDetail>

            <inv:invoiceSummary>
                <inv:homeCurrency>
                    <typ:priceNone>0.00</typ:priceNone>
                    <typ:priceLow>0.00</typ:priceLow>
                    <typ:priceHigh>{((_config.TestData.Amounts.ServiceAmount - _config.TestData.Amounts.AdvanceAmount) / (1 + _config.TestData.Amounts.VatRate)).ToString("F2", CultureInfo.InvariantCulture)}</typ:priceHigh>
                    <typ:priceHighVAT>{((_config.TestData.Amounts.ServiceAmount - _config.TestData.Amounts.AdvanceAmount) - ((_config.TestData.Amounts.ServiceAmount - _config.TestData.Amounts.AdvanceAmount) / (1 + _config.TestData.Amounts.VatRate))).ToString("F2", CultureInfo.InvariantCulture)}</typ:priceHighVAT>
                </inv:homeCurrency>
            </inv:invoiceSummary>
        </inv:invoice>
    </dat:dataPackItem>
</dat:dataPack>";
        }

        public void DisplayCurrentState()
        {
            Console.WriteLine("=== AKTUÁLNÍ STAV VYTVOŘENÝCH DOKLADŮ ===");

            // Scénář A: Zálohová faktura -> Interní doklad -> Vydaná faktura
            bool hasScenarioA = !string.IsNullOrEmpty(AdvanceInvoiceNumber) ||
                               !string.IsNullOrEmpty(InternalDocumentNumber) ||
                               !string.IsNullOrEmpty(IssuedInvoiceNumber);

            // Scénář B: Příjemka -> Přijatá faktura
            bool hasScenarioB = !string.IsNullOrEmpty(ReceiptNumber) ||
                               !string.IsNullOrEmpty(ReceivedInvoiceNumber);

            if (hasScenarioA)
            {
                Console.WriteLine("SCÉNÁŘ A:");
                if (!string.IsNullOrEmpty(AdvanceInvoiceNumber))
                {
                    Console.WriteLine($"✓ Zálohová faktura: {AdvanceInvoiceNumber} (ID: {AdvanceInvoiceId})");
                }
                else
                {
                    Console.WriteLine("○ Zálohová faktura: Nevytvořena");
                }

                if (!string.IsNullOrEmpty(InternalDocumentNumber))
                {
                    Console.WriteLine($"✓ Interní doklad: {InternalDocumentNumber} (ID: {InternalDocumentId})");
                }
                else
                {
                    Console.WriteLine("○ Interní doklad: Nevytvořen");
                }

                if (!string.IsNullOrEmpty(IssuedInvoiceNumber))
                {
                    Console.WriteLine($"✓ Vydaná faktura: {IssuedInvoiceNumber} (ID: {IssuedInvoiceId})");
                }
                else
                {
                    Console.WriteLine("○ Vydaná faktura: Nevytvořena");
                }
            }

            if (hasScenarioB)
            {
                if (hasScenarioA) Console.WriteLine(); // Oddělovač mezi scénáři

                Console.WriteLine("SCÉNÁŘ B:");
                if (!string.IsNullOrEmpty(ReceiptNumber))
                {
                    Console.WriteLine($"✓ Příjemka: {ReceiptNumber} (ID: {ReceiptId})");
                }
                else
                {
                    Console.WriteLine("○ Příjemka: Nevytvořena");
                }

                if (!string.IsNullOrEmpty(ReceivedInvoiceNumber))
                {
                    Console.WriteLine($"✓ Přijatá faktura: {ReceivedInvoiceNumber} (ID: {ReceivedInvoiceId})");
                }
                else
                {
                    Console.WriteLine("○ Přijatá faktura: Nevytvořena");
                }
            }

            // Pokud nejsou žádné doklady, zobraz obecný stav
            if (!hasScenarioA && !hasScenarioB)
            {
                Console.WriteLine("○ Zatím nebyly vytvořeny žádné doklady");
            }

            Console.WriteLine();
        }

        public async Task<bool> LoadAdvanceInvoiceWithLiquidations()
        {
            if (string.IsNullOrEmpty(AdvanceInvoiceId))
            {
                Console.WriteLine("Chyba: Není k dispozici ID zálohové faktury pro načtení.");
                return false;
            }

            try
            {
                string listXml = CreateListAdvanceInvoiceXml();
                var response = await SendXmlRequest(listXml);
                return ParseLiquidationResponse(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Chyba při načítání zálohové faktury: {ex.Message}");
                return false;
            }
        }

        private string CreateListAdvanceInvoiceXml()
        {
            return $@"<?xml version=""1.0"" encoding=""Windows-1250""?>
<dat:dataPack xmlns:dat=""http://www.stormware.cz/schema/version_2/data.xsd""
              xmlns:ftr=""http://www.stormware.cz/schema/version_2/filter.xsd""
              xmlns:lst=""http://www.stormware.cz/schema/version_2/list.xsd""
              xmlns:typ=""http://www.stormware.cz/schema/version_2/type.xsd""
              id=""LIST001""
              ico=""{_ico}""
              application=""PohodaMServerTester""
              version=""2.0""
              note=""Nacitani zalohove faktury s likvidaci"">

    <dat:dataPackItem id=""LIST002"" version=""2.0"">
        <lst:listInvoiceRequest version=""2.0"" invoiceType=""issuedAdvanceInvoice"" invoiceVersion=""2.0"">
            <lst:requestInvoice>
                <ftr:filter>
                    <ftr:id>{AdvanceInvoiceId}</ftr:id>
                </ftr:filter>
            </lst:requestInvoice>
            <lst:restrictionData>
                <lst:liquidations>true</lst:liquidations>
            </lst:restrictionData>
        </lst:listInvoiceRequest>
    </dat:dataPackItem>
</dat:dataPack>";
        }

        private bool ParseLiquidationResponse(string xmlResponse)
        {
            try
            {
                var doc = XDocument.Parse(xmlResponse);

                // Hledání informací o likvidaci v struktuře inv:liquidations/typ:liquidation
                var liquidationElements = doc.Descendants().Where(e => e.Name.LocalName == "liquidation");

                foreach (var liquidation in liquidationElements)
                {
                    // Hledáme typ:id element uvnitř typ:liquidation
                    var idElement = liquidation.Descendants().FirstOrDefault(e => e.Name.LocalName == "id");

                    if (idElement != null)
                    {
                        LiquidationId = idElement.Value;
                        Console.WriteLine($"✓ Nalezeno ID likvidace: {LiquidationId}");
                        return true;
                    }
                }

                Console.WriteLine("⚠ Nebyla nalezena žádná likvidace pro zálohovou fakturu.");
                Console.WriteLine("Zkontrolujte, zda byla zálohová faktura skutečně likvidována v POHODA.");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Chyba při parsování odpovědi s likvidací: {ex.Message}");
                return false;
            }
        }

        public string CreateReceiptXml()
        {
            return $@"<?xml version=""1.0"" encoding=""Windows-1250""?>
<dat:dataPack version=""2.0""
              id=""REC001""
              ico=""{_ico}""
              application=""PohodaMServerTester""
              note=""Prijemka""
              xmlns:dat=""http://www.stormware.cz/schema/version_2/data.xsd""
              xmlns:prj=""http://www.stormware.cz/schema/version_2/prijemka.xsd""
              xmlns:typ=""http://www.stormware.cz/schema/version_2/type.xsd"">

    <dat:dataPackItem version=""2.0"" id=""REC001"">
        <prj:prijemka version=""2.0"">
            <prj:prijemkaHeader>
                <prj:date>{DateTime.Now:yyyy-MM-dd}</prj:date>
                <prj:text>Testovaci prijemka</prj:text>
                <prj:partnerIdentity>
                    <typ:address>
                        <typ:company>{_config.TestData.Customer.Company}</typ:company>
                        <typ:city>{_config.TestData.Customer.City}</typ:city>
                        <typ:street>{_config.TestData.Customer.Street}</typ:street>
                        <typ:zip>{_config.TestData.Customer.Zip}</typ:zip>
                        <typ:ico>{_config.TestData.Customer.Ico}</typ:ico>
                        <typ:dic>{_config.TestData.Customer.Dic}</typ:dic>
                    </typ:address>
                </prj:partnerIdentity>
                <prj:note>Testovaci prijemka pro scenar B</prj:note>
            </prj:prijemkaHeader>

            <prj:prijemkaDetail>
                <prj:prijemkaItem>
                    <prj:text>Testovaci polozka prijemky</prj:text>
                    <prj:quantity>1</prj:quantity>
                    <prj:unit>ks</prj:unit>
                    <prj:payVAT>true</prj:payVAT>
                    <prj:rateVAT>high</prj:rateVAT>
                    <prj:homeCurrency>
                        <typ:unitPrice>{_config.TestData.Amounts.ServiceAmount.ToString("F2", CultureInfo.InvariantCulture)}</typ:unitPrice>
                        <typ:price>{_config.TestData.Amounts.ServiceAmount.ToString("F2", CultureInfo.InvariantCulture)}</typ:price>
                        <typ:priceVAT>{(_config.TestData.Amounts.ServiceAmount * _config.TestData.Amounts.VatRate).ToString("F2", CultureInfo.InvariantCulture)}</typ:priceVAT>
                        <typ:priceSum>{(_config.TestData.Amounts.ServiceAmount * (1 + _config.TestData.Amounts.VatRate)).ToString("F2", CultureInfo.InvariantCulture)}</typ:priceSum>
                    </prj:homeCurrency>
                    <prj:note>Testovaci polozka</prj:note>
                </prj:prijemkaItem>
            </prj:prijemkaDetail>

            <prj:prijemkaSummary>
                <prj:roundingDocument>math2one</prj:roundingDocument>
                <prj:roundingVAT>none</prj:roundingVAT>
                <prj:homeCurrency>
                    <typ:priceNone>0</typ:priceNone>
                    <typ:priceLow>0</typ:priceLow>
                    <typ:priceLowVAT rate=""12"">0</typ:priceLowVAT>
                    <typ:priceLowSum>0</typ:priceLowSum>
                    <typ:priceHigh>{_config.TestData.Amounts.ServiceAmount.ToString("F2", CultureInfo.InvariantCulture)}</typ:priceHigh>
                    <typ:priceHighVAT rate=""21"">{(_config.TestData.Amounts.ServiceAmount * _config.TestData.Amounts.VatRate).ToString("F2", CultureInfo.InvariantCulture)}</typ:priceHighVAT>
                    <typ:priceHighSum>{(_config.TestData.Amounts.ServiceAmount * (1 + _config.TestData.Amounts.VatRate)).ToString("F2", CultureInfo.InvariantCulture)}</typ:priceHighSum>
                    <typ:round>
                        <typ:priceRound>0</typ:priceRound>
                    </typ:round>
                </prj:homeCurrency>
            </prj:prijemkaSummary>
        </prj:prijemka>
    </dat:dataPackItem>
</dat:dataPack>";
        }

        public string CreateReceivedInvoiceXml()
        {
            string linkedDocumentXml = "";

            // Pokud máme ID příjemky, přidáme vazbu
            if (!string.IsNullOrEmpty(ReceiptNumber))
            {
                linkedDocumentXml = $@"
            <inv:links>
                <typ:link>
                    <typ:sourceAgenda>prijemka</typ:sourceAgenda>
                    <typ:sourceDocument>
                        <typ:number>{ReceiptNumber}</typ:number>
                    </typ:sourceDocument>
                </typ:link>
            </inv:links>";
            }

            return $@"<?xml version=""1.0"" encoding=""Windows-1250""?>
<dat:dataPack version=""2.0""
              id=""RIN001""
              ico=""{_ico}""
              application=""PohodaMServerTester""
              note=""Prijata faktura""
              xmlns:dat=""http://www.stormware.cz/schema/version_2/data.xsd""
              xmlns:inv=""http://www.stormware.cz/schema/version_2/invoice.xsd""
              xmlns:typ=""http://www.stormware.cz/schema/version_2/type.xsd"">

    <dat:dataPackItem version=""2.0"" id=""RIN001"">
        <inv:invoice version=""2.0"">{linkedDocumentXml}
            <inv:invoiceHeader>
                <inv:invoiceType>receivedInvoice</inv:invoiceType>
                <inv:date>{DateTime.Now:yyyy-MM-dd}</inv:date>
                <inv:dateTax>{DateTime.Now:yyyy-MM-dd}</inv:dateTax>
                <inv:dateAccounting>{DateTime.Now:yyyy-MM-dd}</inv:dateAccounting>
                <inv:dateDue>{DateTime.Now.AddDays(30):yyyy-MM-dd}</inv:dateDue>
                <inv:text>Prijata faktura s vazbou na prijemku</inv:text>
                <inv:partnerIdentity>
                    <typ:address>
                        <typ:company>{_config.TestData.Customer.Company}</typ:company>
                        <typ:city>{_config.TestData.Customer.City}</typ:city>
                        <typ:street>{_config.TestData.Customer.Street}</typ:street>
                        <typ:zip>{_config.TestData.Customer.Zip}</typ:zip>
                        <typ:ico>{_config.TestData.Customer.Ico}</typ:ico>
                        <typ:dic>{_config.TestData.Customer.Dic}</typ:dic>
                    </typ:address>
                </inv:partnerIdentity>
                <inv:paymentType>
                    <typ:paymentType>draft</typ:paymentType>
                </inv:paymentType>
                <inv:note>Prijata faktura navazana na prijemku {ReceiptNumber}</inv:note>
            </inv:invoiceHeader>

            <inv:invoiceDetail>
                <inv:invoiceItem>
                    <inv:text>Fakturovana polozka dle prijemky</inv:text>
                    <inv:quantity>1</inv:quantity>
                    <inv:unit>ks</inv:unit>
                    <inv:payVAT>true</inv:payVAT>
                    <inv:rateVAT>high</inv:rateVAT>
                    <inv:homeCurrency>
                        <typ:unitPrice>{_config.TestData.Amounts.ServiceAmount.ToString("F2", CultureInfo.InvariantCulture)}</typ:unitPrice>
                        <typ:price>{_config.TestData.Amounts.ServiceAmount.ToString("F2", CultureInfo.InvariantCulture)}</typ:price>
                        <typ:priceVAT>{(_config.TestData.Amounts.ServiceAmount * _config.TestData.Amounts.VatRate).ToString("F2", CultureInfo.InvariantCulture)}</typ:priceVAT>
                        <typ:priceSum>{(_config.TestData.Amounts.ServiceAmount * (1 + _config.TestData.Amounts.VatRate)).ToString("F2", CultureInfo.InvariantCulture)}</typ:priceSum>
                    </inv:homeCurrency>
                    <inv:note>Polozka navazana na prijemku</inv:note>
                </inv:invoiceItem>
            </inv:invoiceDetail>

            <inv:invoiceSummary>
                <inv:roundingDocument>math2one</inv:roundingDocument>
                <inv:roundingVAT>none</inv:roundingVAT>
                <inv:homeCurrency>
                    <typ:priceNone>0</typ:priceNone>
                    <typ:priceLow>0</typ:priceLow>
                    <typ:priceLowVAT rate=""12"">0</typ:priceLowVAT>
                    <typ:priceLowSum>0</typ:priceLowSum>
                    <typ:priceHigh>{_config.TestData.Amounts.ServiceAmount.ToString("F2", CultureInfo.InvariantCulture)}</typ:priceHigh>
                    <typ:priceHighVAT rate=""21"">{(_config.TestData.Amounts.ServiceAmount * _config.TestData.Amounts.VatRate).ToString("F2", CultureInfo.InvariantCulture)}</typ:priceHighVAT>
                    <typ:priceHighSum>{(_config.TestData.Amounts.ServiceAmount * (1 + _config.TestData.Amounts.VatRate)).ToString("F2", CultureInfo.InvariantCulture)}</typ:priceHighSum>
                    <typ:round>
                        <typ:priceRound>0</typ:priceRound>
                    </typ:round>
                </inv:homeCurrency>
            </inv:invoiceSummary>
        </inv:invoice>
    </dat:dataPackItem>
</dat:dataPack>";
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}