<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>PohodaMServerTester</AssemblyName>
    <RootNamespace>PohodaMServerTester</RootNamespace>
    <AssemblyTitle>Pohoda mServer Tester</AssemblyTitle>
    <AssemblyDescription>Aplikace pro testování komunikace s mServerem v ekonomickém systému Pohoda</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Authors>PohodaMServerTester</Authors>
    <Company>Test Company</Company>
    <Product>Pohoda mServer Tester</Product>
    <Copyright>Copyright © 2025</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.6" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

  <ItemGroup>
    <None Update="config.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>