Potřeboval bych vyzkoušet vytváření dokladů v ekonomickém sw Pohoda, pomocí komunikace přes mServer = chci na mServer posílat XML požadavky a v Pohodě by se měly vytvářet doklady
Potřebuju vyzkoušet následující posloupnost. Vytvořit zálohovou fakturu, pro tu následně vytvořit daňový doklad k přijaté platbě (tady jde patrně o interní doklad) a následně fakturu vydanou, na které bude použita ona uhrazena zálohová faktura = bude z faktury vydané odečtena. Smyslem testu je vyzkoušet, zda lze v Pohodě pomocí mServeru provázat vytvářené doklady. Cíl má být takový, že na konci má zálohová faktura vazbu na interní doklad a vydanou fakturu, Interní doklad má vazbu na zálohovou fakturu a Faktura vydaná má vazbu na zálohovou fakturu.
Jak funguje mServer je popsáno zde: https://www.stormware.cz/pohoda/xml/mserver
Jaká je struktura XML pro odesílání dat na mServer je posáno zde: https://www.stormware.cz/pohoda/xml/dokladyimport

Připrav mi jednoduchou aplikaci, kde bych postupně mohl tyto tři doklady vytvořit s nějakými testovacími daty a to tak, aby byly mezi sebou vzájemně provázané.