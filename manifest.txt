Název aplikace: Pohoda mServer Tester
Verze: 1.0.0
Datum vytvoření: 2025-06-24
Autor: AI Assistant
Framework: .NET 8.0

=== OBSAH BALÍČKU ===

Hlavní soubory:
- Program.cs                    - Hlavní aplikační logika
- MServerClient.cs              - Třída pro komunikaci s mServerem  
- PohodaMServerTester.csproj    - Projekt soubor .NET

Dokumentace:
- README.md                     - Detailní dokumentace
- manifest.txt                  - Tento soubor

Spouštěcí soubory:
- spustit.bat                   - Batch soubor pro Windows
- spustit.sh                    - Shell script pro Linux/Mac

Konfigurační soubory:
- config.example.json           - Ukázkový konfigurační soubor

=== RYCHLÉ SPUŠTĚNÍ ===

Windows:
1. Dvojklik na spustit.bat
2. Nebo: dotnet run

Linux/Mac:
1. chmod +x spustit.sh
2. ./spustit.sh
3. Nebo: dotnet run

=== POŽADAVKY ===

- .NET 8.0 SDK/Runtime
- Běžící Pohoda s mServerem
- Uživatel s XML oprávněními

=== KONTAKTY ===

Pro podporu a dotazy se obraťte na dokumentaci Stormware:
https://www.stormware.cz/pohoda/xml/mserver
https://www.stormware.cz/pohoda/xml/dokladyimport

=== CHANGELOG ===

v1.0.0 (2025-06-24):
- Počáteční verze
- Podpora pro zálohové faktury
- Podpora pro interní doklady
- Podpora pro vydané faktury s odpočtem
- Základní XML validace
- Interaktivní konzolové rozhraní
