#!/bin/bash

echo "============================================"
echo "Pohoda mServer Tester"
echo "============================================"
echo

echo "Kompilace aplikace..."
dotnet build
if [ $? -ne 0 ]; then
    echo
    echo "CHYBA: Kompilace selhala!"
    echo "Zkontrolujte, zda máte nainstalovaný .NET 8.0 SDK"
    echo
    read -p "Stiskněte Enter pro ukončení..."
    exit 1
fi

echo
echo "Spouštění aplikace..."
echo
dotnet run

echo
echo "Aplikace ukončena."
read -p "Stiskněte Enter pro ukončení..."
