# Pohoda mServer Tester - Provázané doklady

Konzolová aplikace v C# .NET 8.0 pro testování komunikace s mServerem v ekonomickém systému Pohoda. Aplikace vytváří tři **skutečně provázané** doklady s využitím ID a čísel získaných z odpovědí serveru.

## Klíčové vylepšení ✨

### Správné provázání dokladů
- ✅ **Parsování odpovědí ze serveru** - aplikace získává skutečná ID a čísla vytvořených dokladů
- ✅ **Skutečné vazby** - kaž<PERSON><PERSON> následující doklad obsahuje vazbu na předchozí pomocí skutečných čísel
- ✅ **Automatické propojení** - interní doklad a vydaná faktura automaticky odkazují na zálohovou fakturu

### Zobrazení informací
- 📊 **Aktuální stav** - p<PERSON><PERSON> každým krokem se zobrazí, kter<PERSON> doklady už byly vytvořeny
- 📋 **Detail dokladů** - po vytvoření se zobrazí číslo a ID každého dokladu
- 📈 **Finální souhrn** - na konci přehled všech dokladů a jejich vazeb

### Konfigurace
- ⚙️ **Automatické načítání** - nastavení se načítá z `config.json`
- 🔧 **Flexibilní zadávání** - můžete použít výchozí hodnoty nebo zadat vlastní
- 📝 **Testovací data** - všechna data zákazníka, firmy a částky z konfigurace

## Funkce aplikace

1. **Zálohová faktura** - vytvoření zálohy bez DPH dopadu
2. **Interní doklad** - daňový doklad k přijaté platbě zálohy (s vazbou na zálohovou fakturu)
3. **Vydaná faktura** - faktura za služby s odpočtem zálohy (s vazbou na zálohovou fakturu)

## Požadavky

- .NET 8.0 Runtime nebo SDK
- Běžící Pohoda s nakonfigurovaným mServerem
- Uživatel s oprávněním pro XML komunikaci

## Nastavení mServeru v Pohodě

1. V Pohodě: **Soubor → Účetní jednotky**
2. Menu: **Databáze → Pohoda mServer**
3. Klikněte **Nový**
4. Zadejte název mServeru a vyberte účetní jednotku
5. Nastavte port (standardně 444)
6. Vytvořte uživatele s oprávněním pro XML komunikaci

## Instalace a spuštění

### Varianta 1: Pomocí .NET CLI

```bash
# Kompilace aplikace
dotnet build

# Spuštění aplikace
dotnet run
```

### Varianta 2: Pomocí batch souboru (Windows)

```bash
# Spuštění pomocí přiloženého batch souboru
spustit.bat
```

### Varianta 3: Přímé spuštění exe

```bash
# Po kompilaci spustit přímo
.\bin\Debug\net8.0\PohodaMServerTester.exe
```

## Použití

1. **Spusťte aplikaci**
2. **Zadejte připojovací údaje:**
   - Aplikace načte výchozí hodnoty z `config.json`
   - Můžete stisknout Enter pro použití výchozích hodnot
   - Nebo zadat vlastní URL, IČO, uživatelské jméno a heslo

3. **Postupujte podle kroků:**
   - Aplikace postupně nabídne vytvoření všech tří dokladů
   - Před odesláním si můžete prohlédnout vygenerované XML
   - Potvrdíte odesílání každého dokladu

4. **Kontrola výsledků:**
   - Zkontrolujte v Pohodě vytvorené doklady
   - Ověřte vazby mezi doklady

## Konfigurace (config.json)

Aplikace automaticky načítá nastavení ze souboru `config.json`. Pokud soubor neexistuje, použijí se výchozí hodnoty.

### Struktura konfigurace:

```json
{
  "mServer": {
    "url": "http://localhost:444",
    "username": "admin",
    "password": "heslo",
    "ico": "********"
  },
  "testData": {
    "customer": {
      "company": "Testovací zákazník s.r.o.",
      "city": "Praha",
      "street": "Testovací 123",
      "zip": "11000",
      "ico": "********",
      "dic": "**********"
    },
    "myCompany": {
      "company": "Moje firma s.r.o.",
      "accountNo": "*********",
      "bankCode": "0100"
    },
    "amounts": {
      "advanceAmount": 10000.00,
      "serviceAmount": 15000.00,
      "vatRate": 0.21
    }
  }
}
```

### Použití konfigurace:
- **Při spuštění** se zobrazí výchozí hodnoty v hranatých závorkách
- **Stisknutím Enter** použijete výchozí hodnotu z konfigurace
- **Zadáním vlastní hodnoty** přepíšete výchozí nastavení
- **Testovací data** se automaticky použijí ve všech XML dokladech

## Struktura dokladů a jejich vazeb

### 1. Zálohová faktura
- **Typ:** `issuedAdvanceInvoice`
- **Číslo:** `ZF` + časové razítko
- **Bez DPH** při vystavení
- **Vazby:** Bude odkazována z interního dokladu a vydané faktury

### 2. Interní doklad
- **Typ:** `intDoc`
- **Číslo:** `ID` + časové razítko
- **S DPH** při úhradě zálohy
- **Vazba:** `<int:linkedDocument>` → odkazuje na skutečné číslo zálohové faktury
- **Poznámka:** Obsahuje text s vazbou na zálohovou fakturu

### 3. Vydaná faktura
- **Typ:** `issuedInvoice`
- **Číslo:** `FA` + časové razítko
- **S odpočtem zálohy**
- **Vazby:**
  - `<inv:linkedDocument>` → odkazuje na zálohovou fakturu
  - `<inv:invoiceAdvancePaymentItem>` → odpočet zálohy se skutečným číslem

## Technické detaily vazeb

### Parsování odpovědí
Aplikace parsuje XML odpovědi ze serveru a hledá:
- Atribut `state="ok"` pro úspěšně vytvořené doklady
- ID dokladu z atributu `id`
- Číslo dokladu z elementu `number`

### XML elementy pro vazby
- **Interní doklad:** `<int:linkedDocument><typ:sourceDocument><typ:number>`
- **Vydaná faktura:** `<inv:linkedDocument><typ:sourceDocument><typ:number>`
- **Odpočet zálohy:** `<inv:invoiceAdvancePaymentItem><inv:sourceDocument><typ:number>`

## Řešení problémů

### Chyba připojení
- Zkontrolujte, zda běží mServer v Pohodě
- Ověřte správnost URL a portu
- Zkontrolujte uživatelské údaje

### XML chyby
- Zkontrolujte XML Log v Pohodě
- Ověřte oprávnění uživatele
- Zkontrolujte IČO účetní jednotky

### Vazby mezi doklady
- Zkontrolujte v Pohodě číselné řady dokladů
- Ověřte, zda se doklady vytvořily se správnými čísly
- Zkontrolujte vazby v detailu dokladů
- **Nové:** Aplikace nyní zobrazuje skutečná čísla a ID dokladů
- **Nové:** Vazby se vytvářejí automaticky na základě odpovědí ze serveru

### Parsování odpovědí
- Pokud se nezobrazují ID dokladů, zkontrolujte formát XML odpovědi
- Aplikace hledá atribut `state="ok"` v odpovědi
- Zkontrolujte, zda mServer vrací správné XML odpovědi

## Technické informace

- **Framework:** .NET 8.0
- **Typ aplikace:** Console Application
- **HTTP klient:** System.Net.Http.HttpClient
- **Autentifikace:** Basic Authentication
- **XML formát:** Pohoda XML verze 2.0

## Soubory aplikace

- `Program.cs` - hlavní logika aplikace
- `MServerClient.cs` - třída pro komunikaci s mServerem
- `PohodaMServerTester.csproj` - projekt soubor
- `README.md` - tento soubor
- `spustit.bat` - batch soubor pro Windows

## Podpora

Aplikace je vytvořena pro testovací účely. Pro produkční použití doporučujeme rozšíření o:
- Lepší error handling
- Konfigurační soubor
- Logging
- Validaci XML proti XSD schématům

---

© 2025 - Pohoda mServer Tester
