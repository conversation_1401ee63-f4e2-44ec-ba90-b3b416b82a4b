using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using Microsoft.Extensions.Configuration;

namespace PohodaMServerTester
{
    class Program
    {
        static async Task Main(string[] args)
        {
            // Add this line before any code that uses Windows-1250 encoding
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            Console.WriteLine("=== Pohoda mServer Tester ===");
            Console.WriteLine("Aplikace pro testování vytváření provázaných dokladů v Pohodě");
            Console.WriteLine();

            // Načtení konfigurace
            var config = LoadConfiguration();

            // Získání přihlašovacích údajů s možností použít výchozí hodnoty z konfigurace
            Console.WriteLine("Zadejte nastavení mServeru (pro použití výchozích hodnot z config.json stiskněte Enter):");
            Console.WriteLine();

            Console.Write($"URL mServeru [{config.MServer.Url}]: ");
            string serverUrl = Console.ReadLine() ?? "";
            if (string.IsNullOrWhiteSpace(serverUrl))
                serverUrl = config.MServer.Url;

            Console.Write($"IČO účetní jednotky [{config.MServer.Ico}]: ");
            string ico = Console.ReadLine() ?? "";
            if (string.IsNullOrWhiteSpace(ico))
                ico = config.MServer.Ico;

            Console.Write($"Uživatelské jméno [{config.MServer.Username}]: ");
            string username = Console.ReadLine() ?? "";
            if (string.IsNullOrWhiteSpace(username))
                username = config.MServer.Username;

            Console.Write($"Heslo [{(string.IsNullOrEmpty(config.MServer.Password) ? "" : "***")}]: ");
            string password = ReadPassword();
            if (string.IsNullOrWhiteSpace(password))
                password = config.MServer.Password;

            Console.WriteLine();
            Console.WriteLine("Připojuji se k mServeru...");
            Console.WriteLine($"Používá se konfigurace: {config.TestData.Customer.Company}");

            var client = new MServerClient(serverUrl, username, password, ico, config);

            try
            {
                // Test připojení
                Console.WriteLine("Testování připojení...");
                var testResult = await client.TestConnection();
                Console.WriteLine($"Test připojení: {(testResult ? "ÚSPĚŠNÝ" : "NEÚSPĚŠNÝ")}");

                if (!testResult)
                {
                    Console.WriteLine("Nelze se připojit k mServeru. Zkontrolujte nastavení.");
                    return;
                }

                Console.WriteLine();
                Console.WriteLine("=== KROK 1: Vytvoření zálohové faktury ===");
                client.DisplayCurrentState();
                await ProcessStep(client, "advance", "zálohové faktury");

                // Pokud byla zálohová faktura vytvořena, zobrazíme upozornění o likvidaci
                if (!string.IsNullOrEmpty(client.AdvanceInvoiceNumber))
                {
                    await ProcessLiquidationStep(client);
                }

                Console.WriteLine();
                Console.WriteLine("=== KROK 2: Vytvoření interního dokladu ===");
                client.DisplayCurrentState();
                await ProcessStep(client, "internal", "interního dokladu");

                Console.WriteLine();
                Console.WriteLine("=== KROK 3: Vytvoření vydané faktury s odpočtem zálohy ===");
                client.DisplayCurrentState();
                await ProcessStep(client, "issued", "vydané faktury");

                Console.WriteLine();
                Console.WriteLine("=== DOKONČENO ===");
                Console.WriteLine("Všechny tři doklady byly vytvořeny. Zkontrolujte v Pohodě jejich vazby.");

                // Zobrazení souhrnu všech vytvořených dokladů
                DisplayFinalSummary(client);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Chyba: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Stiskněte libovolnou klávesu pro ukončení...");
            Console.ReadKey();
        }

        private static async Task ProcessLiquidationStep(MServerClient client)
        {
            Console.WriteLine();
            Console.WriteLine("=== UPOZORNĚNÍ: NUTNÁ RUČNÍ LIKVIDACE ===");
            Console.WriteLine($"Zálohová faktura {client.AdvanceInvoiceNumber} byla vytvořena.");
            Console.WriteLine();
            Console.WriteLine("DŮLEŽITÉ: Nyní musíte v programu POHODA provést následující kroky:");
            Console.WriteLine("1. Otevřete zálohovou fakturu v POHODA");
            Console.WriteLine("2. Proveďte její likvidaci pomocí bankovního pohybu");
            Console.WriteLine("3. Uložte změny");
            Console.WriteLine();
            Console.WriteLine("Bez této likvidace nebude možné správně vytvořit interní doklad!");
            Console.WriteLine();

            // Čekání na potvrzení od uživatele
            while (true)
            {
                Console.Write("Potvrdíte, že jste provedli likvidaci zálohové faktury v POHODA? (a/n): ");
                string response = Console.ReadLine()?.ToLower() ?? "";

                if (response == "a" || response == "ano" || response == "y" || response == "yes")
                {
                    Console.WriteLine();
                    Console.WriteLine("Načítání informací o likvidaci...");

                    // Načtení zálohové faktury s informacemi o likvidaci
                    bool success = await client.LoadAdvanceInvoiceWithLiquidations();

                    if (success)
                    {
                        Console.WriteLine($"✓ Likvidace načtena (ID: {client.LiquidationId})");
                    }
                    else
                    {
                        Console.WriteLine("⚠ Nepodařilo se načíst informace o likvidaci.");
                        Console.WriteLine("Interní doklad bude vytvořen bez správné vazby na likvidaci.");
                    }
                    break;
                }
                else if (response == "n" || response == "ne" || response == "no")
                {
                    Console.WriteLine("Prosím, proveďte nejdříve likvidaci zálohové faktury v POHODA.");
                    Console.WriteLine("Stiskněte libovolnou klávesu a poté pokračujte...");
                    Console.ReadKey();
                    Console.WriteLine();
                }
                else
                {
                    Console.WriteLine("Neplatná odpověď. Zadejte 'a' pro ano nebo 'n' pro ne.");
                }
            }
        }

        private static async Task ProcessStep(MServerClient client, string documentType, string documentName)
        {
            Console.Write($"Chcete vytvořit {documentName}? (a/n): ");
            string response = Console.ReadLine()?.ToLower() ?? "";

            if (response == "a" || response == "ano" || response == "y" || response == "yes")
            {
                Console.WriteLine($"Vytváření {documentName}...");

                string xml = documentType switch
                {
                    "advance" => client.CreateAdvanceInvoiceXml(),
                    "internal" => client.CreateInternalDocumentXml(),
                    "issued" => client.CreateIssuedInvoiceXml(),
                    _ => throw new ArgumentException("Neznámý typ dokladu")
                };

                var result = await client.SendXmlRequest(xml, documentType);

                // Kontrola úspěšnosti
                if (result.Contains("state=\"ok\""))
                {
                    Console.WriteLine($"✓ {documentName} byla úspěšně vytvořena.");
                    DisplayDocumentInfo(client, documentType);
                }
                else
                {
                    Console.WriteLine($"✗ Chyba při vytváření {documentName}:");
                    Console.WriteLine(FormatXml(result));
                }
            }
            else
            {
                Console.WriteLine($"Přeskočeno vytvoření {documentName}.");
            }
        }

        private static void DisplayDocumentInfo(MServerClient client, string documentType)
        {
            Console.WriteLine();
            Console.WriteLine("=== INFORMACE O VYTVOŘENÉM DOKLADU ===");

            switch (documentType.ToLower())
            {
                case "advance":
                    if (!string.IsNullOrEmpty(client.AdvanceInvoiceNumber))
                    {
                        Console.WriteLine($"Zálohová faktura: {client.AdvanceInvoiceNumber} (ID: {client.AdvanceInvoiceId})");
                        if (!string.IsNullOrEmpty(client.LiquidationId))
                        {
                            Console.WriteLine($"ID likvidace: {client.LiquidationId}");
                        }
                    }
                    break;
                case "internal":
                    if (!string.IsNullOrEmpty(client.InternalDocumentNumber))
                    {
                        Console.WriteLine($"Interní doklad: {client.InternalDocumentNumber} (ID: {client.InternalDocumentId})");
                        if (!string.IsNullOrEmpty(client.AdvanceInvoiceNumber))
                        {
                            Console.WriteLine($"Vazba na zálohovou fakturu: {client.AdvanceInvoiceNumber}");
                        }
                        if (!string.IsNullOrEmpty(client.LiquidationId))
                        {
                            Console.WriteLine($"Vazba na likvidaci: {client.LiquidationId}");
                        }
                    }
                    break;
                case "issued":
                    if (!string.IsNullOrEmpty(client.IssuedInvoiceNumber))
                    {
                        Console.WriteLine($"Vydaná faktura: {client.IssuedInvoiceNumber} (ID: {client.IssuedInvoiceId})");
                        if (!string.IsNullOrEmpty(client.AdvanceInvoiceNumber))
                        {
                            Console.WriteLine($"Odpočet zálohy: {client.AdvanceInvoiceNumber}");
                        }
                    }
                    break;
            }
            Console.WriteLine();
        }

        private static void DisplayFinalSummary(MServerClient client)
        {
            Console.WriteLine();
            Console.WriteLine("=== SOUHRN VYTVOŘENÝCH DOKLADŮ A JEJICH VAZEB ===");
            Console.WriteLine();

            if (!string.IsNullOrEmpty(client.AdvanceInvoiceNumber))
            {
                Console.WriteLine($"1. ZÁLOHOVÁ FAKTURA: {client.AdvanceInvoiceNumber}");
                Console.WriteLine($"   ID: {client.AdvanceInvoiceId}");
                if (!string.IsNullOrEmpty(client.LiquidationId))
                {
                    Console.WriteLine($"   ID likvidace: {client.LiquidationId}");
                }
                Console.WriteLine($"   Vazby: Bude odkazována z interního dokladu a vydané faktury");
                Console.WriteLine();
            }

            if (!string.IsNullOrEmpty(client.InternalDocumentNumber))
            {
                Console.WriteLine($"2. INTERNÍ DOKLAD: {client.InternalDocumentNumber}");
                Console.WriteLine($"   ID: {client.InternalDocumentId}");
                if (!string.IsNullOrEmpty(client.AdvanceInvoiceNumber))
                {
                    Console.WriteLine($"   Vazba na: Zálohová faktura {client.AdvanceInvoiceNumber}");
                }
                if (!string.IsNullOrEmpty(client.LiquidationId))
                {
                    Console.WriteLine($"   Vazba na: Likvidace {client.LiquidationId}");
                }
                Console.WriteLine();
            }

            if (!string.IsNullOrEmpty(client.IssuedInvoiceNumber))
            {
                Console.WriteLine($"3. VYDANÁ FAKTURA: {client.IssuedInvoiceNumber}");
                Console.WriteLine($"   ID: {client.IssuedInvoiceId}");
                if (!string.IsNullOrEmpty(client.AdvanceInvoiceNumber))
                {
                    Console.WriteLine($"   Odpočet zálohy: {client.AdvanceInvoiceNumber}");
                    Console.WriteLine($"   Vazba na: Zálohová faktura {client.AdvanceInvoiceNumber}");
                }
                Console.WriteLine();
            }

            Console.WriteLine("VÝSLEDEK:");
            Console.WriteLine("- Zálohová faktura byla likvidována bankovním pohybem");
            Console.WriteLine("- Interní doklad má vazbu na likvidaci zálohové faktury");
            Console.WriteLine("- Vydaná faktura má vazbu na zálohovou fakturu (odpočet zálohy)");
            Console.WriteLine();
            Console.WriteLine("Zkontrolujte v programu POHODA, zda jsou vazby správně vytvořeny.");
        }

        private static Configuration LoadConfiguration()
        {
            try
            {
                var builder = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("config.json", optional: true, reloadOnChange: false);

                var configuration = builder.Build();
                var config = new Configuration();
                configuration.Bind(config);

                Console.WriteLine("✓ Konfigurace načtena z config.json");
                return config;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠ Nepodařilo se načíst config.json: {ex.Message}");
                Console.WriteLine("Používají se výchozí hodnoty.");
                return new Configuration();
            }
        }

        private static string ReadPassword()
        {
            StringBuilder password = new StringBuilder();
            ConsoleKeyInfo key;

            do
            {
                key = Console.ReadKey(true);
                if (key.Key != ConsoleKey.Backspace && key.Key != ConsoleKey.Enter)
                {
                    password.Append(key.KeyChar);
                    Console.Write("*");
                }
                else if (key.Key == ConsoleKey.Backspace && password.Length > 0)
                {
                    password.Remove(password.Length - 1, 1);
                    Console.Write("\b \b");
                }
            }
            while (key.Key != ConsoleKey.Enter);

            Console.WriteLine();
            return password.ToString();
        }

        private static string FormatXml(string xml)
        {
            try
            {
                var doc = XDocument.Parse(xml);
                return doc.ToString();
            }
            catch
            {
                return xml;
            }
        }
    }
}