using System;

namespace PohodaMServerTester
{
    public class Configuration
    {
        public MServerConfig MServer { get; set; } = new();
        public TestDataConfig TestData { get; set; } = new();
    }

    public class MServerConfig
    {
        public string Url { get; set; } = "http://localhost:444";
        public string Username { get; set; } = "";
        public string Password { get; set; } = "";
        public string Ico { get; set; } = "";
    }

    public class TestDataConfig
    {
        public CustomerConfig Customer { get; set; } = new();
        public MyCompanyConfig MyCompany { get; set; } = new();
        public AmountsConfig Amounts { get; set; } = new();
    }

    public class CustomerConfig
    {
        public string Company { get; set; } = "Testovací zákazník s.r.o.";
        public string City { get; set; } = "Praha";
        public string Street { get; set; } = "Testovací 123";
        public string Zip { get; set; } = "11000";
        public string Ico { get; set; } = "********";
        public string Dic { get; set; } = "CZ********";
    }

    public class MyCompanyConfig
    {
        public string Company { get; set; } = "Moje firma s.r.o.";
        public string AccountNo { get; set; } = "*********";
        public string BankCode { get; set; } = "0100";
    }

    public class AmountsConfig
    {
        public decimal AdvanceAmount { get; set; } = 10000.00m;
        public decimal ServiceAmount { get; set; } = 15000.00m;
        public decimal VatRate { get; set; } = 0.21m;
    }
}
